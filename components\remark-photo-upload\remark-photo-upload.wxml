<view class="remark-photo-upload">
  <!-- 图片容器 -->
  <view class="photo-container">
    <!-- 已上传的图片 -->
    <view wx:for="{{photoList}}" wx:key="index" class="photo-item">
      <image
        src="{{item}}"
        class="photo-preview"
        mode="aspectFill"
        bindtap="previewImage"
        data-url="{{item}}"
      ></image>
      <view wx:if="{{!disabled}}" class="photo-delete" catchtap="deletePhoto" data-index="{{index}}">
        ×
      </view>
    </view>
    
    <!-- 上传按钮 -->
    <view 
      wx:if="{{!disabled && photoList.length < maxCount && !uploading}}" 
      class="photo-upload" 
      bindtap="chooseImage"
    >
      <view class="upload-icon">+</view>
      <text class="upload-text">{{placeholder}}</text>
    </view>

    <!-- 上传中状态 -->
    <view wx:if="{{uploading}}" class="photo-uploading">
      <view class="uploading-icon">
        <view class="loading-spinner"></view>
      </view>
      <text class="uploading-text">上传中...</text>
    </view>
  </view>

  <!-- 图片数量提示 -->
  <view wx:if="{{photoList.length > 0}}" class="photo-count">
    已选择 {{photoList.length}}/{{maxCount}} 张图片
  </view>
</view>
