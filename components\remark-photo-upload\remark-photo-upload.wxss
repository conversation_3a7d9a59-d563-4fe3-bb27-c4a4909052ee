.remark-photo-upload {
  width: 100%;
}

.photo-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.photo-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f5f5f5;
}

.photo-preview {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.photo-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
  z-index: 10;
}

.photo-upload {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #d1d5db;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9fafb;
  transition: all 0.3s ease;
}

.photo-upload:active {
  background-color: #f3f4f6;
  border-color: #3b82f6;
}

.upload-icon {
  font-size: 48rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
  font-weight: 300;
}

.upload-text {
  font-size: 24rpx;
  color: #6b7280;
  text-align: center;
  line-height: 1.2;
}

.photo-uploading {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9fafb;
}

.uploading-icon {
  margin-bottom: 8rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #e5e7eb;
  border-top: 3rpx solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.uploading-text {
  font-size: 24rpx;
  color: #6b7280;
}

.photo-count {
  font-size: 24rpx;
  color: #6b7280;
  text-align: right;
  margin-top: 8rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .photo-item,
  .photo-upload,
  .photo-uploading {
    width: 140rpx;
    height: 140rpx;
  }
  
  .upload-icon {
    font-size: 40rpx;
  }
  
  .upload-text,
  .uploading-text {
    font-size: 22rpx;
  }
}

/* 禁用状态 */
.remark-photo-upload[disabled] .photo-upload {
  opacity: 0.5;
  pointer-events: none;
}
