Component({
  properties: {
    // 已选择的图片列表
    photoList: {
      type: Array,
      value: []
    },
    // 最大图片数量
    maxCount: {
      type: Number,
      value: 9
    },
    // 是否禁用上传
    disabled: {
      type: Boolean,
      value: false
    },
    // 上传key前缀
    keyPrefix: {
      type: String,
      value: 'remark-photos/'
    },
    // 占位提示文字
    placeholder: {
      type: String,
      value: '添加说明图片'
    }
  },

  data: {
    // 是否正在上传
    uploading: false
  },

  methods: {
    /**
     * 选择图片
     */
    chooseImage() {
      if (this.data.disabled || this.data.uploading) {
        return;
      }

      const { photoList, maxCount } = this.data;
      const remainCount = maxCount - photoList.length;
      
      if (remainCount <= 0) {
        wx.showToast({
          title: `最多只能上传${maxCount}张照片`,
          icon: 'none'
        });
        return;
      }

      // 使用页面扩展的上传功能
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      if (!currentPage || !currentPage.uploadImage) {
        wx.showToast({
          title: '上传功能不可用',
          icon: 'none'
        });
        return;
      }

      this.setData({ uploading: true });

      const { keyPrefix } = this.data;
      const timestamp = Date.now();
      const finalKeyPrefix = `${keyPrefix}${timestamp}/`;

      currentPage.uploadImage(
        currentPage,
        '', // 存储字段
        finalKeyPrefix, // 上传key前缀
        remainCount // 最大数量
      ).then(res => {
        // 将新上传的照片与已有照片合并
        const newPhotoList = [...photoList, ...res];

        // 检查是否超过最大数量限制
        const finalPhotoList = newPhotoList.slice(0, this.data.maxCount);

        // 触发父组件更新
        this.triggerEvent('change', {
          photoList: finalPhotoList
        });

        this.setData({ uploading: false });

        wx.showToast({
          title: `上传成功，共${finalPhotoList.length}张照片`,
          icon: 'success'
        });
      }).catch(error => {
        console.error('上传失败:', error);
        this.setData({ uploading: false });
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      });
    },

    /**
     * 删除图片
     */
    deletePhoto(e) {
      if (this.data.disabled) {
        return;
      }

      const index = e.currentTarget.dataset.index;
      const photoList = [...this.data.photoList];
      photoList.splice(index, 1);
      
      // 触发父组件更新
      this.triggerEvent('change', {
        photoList
      });
    },

    /**
     * 预览图片
     */
    previewImage(e) {
      const url = e.currentTarget.dataset.url;
      const { photoList } = this.data;
      
      wx.previewImage({
        current: url,
        urls: photoList
      });
    }
  }
});
