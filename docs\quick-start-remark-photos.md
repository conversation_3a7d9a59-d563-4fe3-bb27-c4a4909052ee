# 备注图片上传功能 - 快速启动指南

## 🚀 快速测试

### 1. 访问演示页面

在微信开发者工具中，导航到以下页面进行测试：

```
pages/order-create/index
```

### 2. 测试功能

1. **上传图片**: 点击"添加说明图片"按钮
2. **预览图片**: 点击已上传的图片进行预览
3. **删除图片**: 点击图片右上角的"×"按钮删除
4. **查看数据**: 在控制台查看图片数据变化

### 3. 在现有页面中集成

如果您想在现有的订单创建页面中集成此功能，请按以下步骤操作：

#### 步骤1: 添加组件引用

在您的页面 `.json` 文件中添加：

```json
{
  "usingComponents": {
    "remark-photo-upload": "/components/remark-photo-upload/remark-photo-upload"
  }
}
```

#### 步骤2: 在模板中添加组件

在备注说明区域添加以下代码：

```xml
<!-- 在备注文本框下方添加 -->
<view class="remark-photos">
  <view class="photos-title">说明图片</view>
  <remark-photo-upload
    photoList="{{orderInfo.remarkPhotos}}"
    maxCount="9"
    keyPrefix="order-remark-photos/"
    placeholder="添加说明图片"
    bind:change="onRemarkPhotosChange"
  />
</view>
```

#### 步骤3: 添加数据处理逻辑

在页面的 `.js` 文件中添加：

```javascript
// 在data中添加remarkPhotos字段
data: {
  orderInfo: {
    // 其他字段...
    remark: '',
    remarkPhotos: [] // 新增字段
  }
},

// 添加图片变更处理方法
onRemarkPhotosChange(e) {
  const photoList = e.detail.photoList;
  this.setData({
    'orderInfo.remarkPhotos': photoList
  });
  console.log('备注图片更新:', photoList);
}
```

#### 步骤4: 更新提交逻辑

在订单提交时包含remarkPhotos字段：

```javascript
submitOrder() {
  const { orderInfo } = this.data;
  
  const orderData = {
    ...orderInfo,
    remarkPhotos: orderInfo.remarkPhotos || [] // 确保字段存在
  };
  
  // 调用API提交订单
  console.log('提交订单数据:', orderData);
}
```

## 🎨 样式自定义

如果需要自定义样式，可以在页面的 `.wxss` 文件中添加：

```css
.remark-photos {
  margin-top: 24rpx;
}

.photos-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

/* 自定义组件样式 */
.remark-photo-upload .photo-upload {
  border-color: #your-brand-color;
}
```

## 📝 数据格式

### 输入数据格式
```javascript
{
  photoList: ['url1', 'url2', 'url3'], // 图片URL数组
  maxCount: 9,                         // 最大图片数量
  keyPrefix: 'order-remark-photos/',   // 上传路径前缀
  placeholder: '添加说明图片'           // 提示文字
}
```

### 输出数据格式
```javascript
// change事件返回的数据
{
  detail: {
    photoList: ['url1', 'url2', 'url3'] // 更新后的图片URL数组
  }
}
```

## 🔧 常见问题

### Q: 图片上传失败怎么办？
A: 检查以下几点：
1. 确保页面继承了 `page-extend.js`
2. 检查网络连接
3. 确认天翼云存储配置正确

### Q: 如何限制图片大小？
A: 可以在 `page-extend.js` 的 `uploadImage` 方法中添加图片大小检查

### Q: 如何自定义上传路径？
A: 通过 `keyPrefix` 属性设置，例如：`keyPrefix="custom-path/"`

### Q: 如何禁用上传功能？
A: 设置 `disabled="{{true}}"` 属性

## 📱 测试清单

- [ ] 单张图片上传
- [ ] 多张图片上传
- [ ] 达到最大数量限制
- [ ] 图片预览功能
- [ ] 图片删除功能
- [ ] 网络异常处理
- [ ] 不同设备适配
- [ ] 数据提交验证

## 🚀 部署注意事项

1. 确保后端API支持 `remarkPhotos` 字段
2. 数据库表结构包含相应字段
3. 图片存储空间充足
4. 网络环境稳定

完成以上步骤后，您就可以在订单创建流程中使用备注图片上传功能了！
