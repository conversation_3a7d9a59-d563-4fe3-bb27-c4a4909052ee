Page({
  data: {
    // 订单信息
    orderInfo: {
      serviceAddress: '陕西省西安市雁塔区潘家庄街道大雁塔路39号',
      customerPhone: '18629099480',
      expectTime: '',
      serviceFee: 58.00,
      discountAmount: 11.60,
      totalFee: 46.40,
      remark: '',
      remarkPhotos: [] // 备注说明图片
    },
    
    // 优惠券信息
    couponInfo: {
      title: '优惠选择',
      selected: false,
      discount: '1张5元用'
    },

    // 时间选择器
    showTimePicker: false,
    selectedTime: ''
  },

  onLoad(options) {
    // 页面加载时的初始化逻辑
    console.log('订单创建页面加载', options);
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 选择期望上门时间
   */
  selectTime() {
    this.setData({
      showTimePicker: true
    });
  },

  /**
   * 时间选择确认
   */
  onTimeSelected(e) {
    const selectedTime = e.detail.time;
    this.setData({
      'orderInfo.expectTime': selectedTime,
      showTimePicker: false
    });
  },

  /**
   * 时间选择取消
   */
  onTimeCancel() {
    this.setData({
      showTimePicker: false
    });
  },

  /**
   * 备注内容输入
   */
  onRemarkInput(e) {
    const remark = e.detail.value;
    this.setData({
      'orderInfo.remark': remark
    });
  },

  /**
   * 备注图片变更
   */
  onRemarkPhotosChange(e) {
    const photoList = e.detail.photoList;
    this.setData({
      'orderInfo.remarkPhotos': photoList
    });
    console.log('备注图片更新:', photoList);
  },

  /**
   * 更改服务地址
   */
  editAddress() {
    wx.showToast({
      title: '更改地址功能',
      icon: 'none'
    });
  },

  /**
   * 选择优惠券
   */
  selectCoupon() {
    wx.showToast({
      title: '选择优惠券',
      icon: 'none'
    });
  },

  /**
   * 提交订单
   */
  submitOrder() {
    const { orderInfo } = this.data;
    
    // 验证必填字段
    if (!orderInfo.expectTime) {
      wx.showToast({
        title: '请选择期望上门时间',
        icon: 'none'
      });
      return;
    }

    // 构建订单数据
    const orderData = {
      ...orderInfo,
      // 确保remarkPhotos字段存在
      remarkPhotos: orderInfo.remarkPhotos || []
    };

    console.log('提交订单数据:', orderData);

    // 这里应该调用API提交订单
    wx.showLoading({
      title: '提交中...'
    });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '订单提交成功',
        icon: 'success'
      });
      
      // 跳转到订单详情或订单列表页面
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 2000);
  }
});
