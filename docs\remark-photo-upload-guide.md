# 备注说明图片上传功能使用指南

## 功能概述

本功能为订单创建流程中的备注说明字段添加了图片上传功能，支持用户上传最多9张说明图片，用于更详细地描述订单相关信息。

## 组件特性

- ✅ 支持最多9张图片上传
- ✅ 图片预览和删除功能
- ✅ 上传进度显示
- ✅ 响应式设计，适配不同屏幕尺寸
- ✅ 与现有图片上传机制完全集成
- ✅ 支持可选的remarkPhotos属性

## 文件结构

```
components/remark-photo-upload/
├── remark-photo-upload.js      # 组件逻辑
├── remark-photo-upload.wxml    # 组件模板
├── remark-photo-upload.wxss    # 组件样式
└── remark-photo-upload.json    # 组件配置

pages/order-create/             # 示例页面
├── index.js                    # 页面逻辑
├── index.wxml                  # 页面模板
├── index.wxss                  # 页面样式
└── index.json                  # 页面配置
```

## 使用方法

### 1. 在页面配置中引入组件

在需要使用的页面的 `.json` 文件中添加组件引用：

```json
{
  "usingComponents": {
    "remark-photo-upload": "/components/remark-photo-upload/remark-photo-upload"
  }
}
```

### 2. 在页面模板中使用组件

```xml
<remark-photo-upload
  photoList="{{orderInfo.remarkPhotos}}"
  maxCount="9"
  keyPrefix="order-remark-photos/"
  placeholder="添加说明图片"
  bind:change="onRemarkPhotosChange"
/>
```

### 3. 在页面逻辑中处理数据

```javascript
Page({
  data: {
    orderInfo: {
      remark: '',
      remarkPhotos: [] // 备注说明图片数组
    }
  },

  /**
   * 备注图片变更处理
   */
  onRemarkPhotosChange(e) {
    const photoList = e.detail.photoList;
    this.setData({
      'orderInfo.remarkPhotos': photoList
    });
    console.log('备注图片更新:', photoList);
  }
});
```

## 组件属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| photoList | Array | [] | 否 | 已选择的图片URL数组 |
| maxCount | Number | 9 | 否 | 最大图片数量 |
| disabled | Boolean | false | 否 | 是否禁用上传功能 |
| keyPrefix | String | 'remark-photos/' | 否 | 上传文件的key前缀 |
| placeholder | String | '添加说明图片' | 否 | 上传按钮的提示文字 |

## 组件事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| change | 图片列表变更时触发 | e.detail.photoList: 当前图片URL数组 |

## API接口集成

### 订单创建接口

在提交订单时，需要将 `remarkPhotos` 字段包含在订单数据中：

```javascript
const orderData = {
  // 其他订单字段...
  remark: '备注文字内容',
  remarkPhotos: ['图片URL1', '图片URL2', ...] // 可选字段
};
```

### 后端数据结构建议

```sql
-- 订单表添加备注图片字段
ALTER TABLE orders ADD COLUMN remark_photos TEXT COMMENT '备注说明图片JSON数组';

-- 或者创建单独的订单图片表
CREATE TABLE order_remark_photos (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL,
  photo_url VARCHAR(500) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_order_id (order_id)
);
```

## 样式自定义

组件提供了完整的CSS类名，可以通过全局样式进行自定义：

```css
/* 自定义上传按钮样式 */
.remark-photo-upload .photo-upload {
  border-color: #your-brand-color;
}

/* 自定义图片预览样式 */
.remark-photo-upload .photo-preview {
  border-radius: 8rpx;
}
```

## 注意事项

1. **图片上传依赖**: 组件依赖页面扩展中的 `uploadImage` 方法，确保页面继承了 `page-extend.js`
2. **存储空间**: 图片上传到天翼云存储，确保有足够的存储空间配额
3. **网络环境**: 图片上传需要良好的网络环境，建议添加重试机制
4. **图片大小**: 建议限制单张图片大小，避免上传过大的图片影响用户体验

## 测试建议

1. 测试不同数量的图片上传（1张、多张、达到上限）
2. 测试网络异常情况下的上传失败处理
3. 测试图片删除功能
4. 测试在不同设备和屏幕尺寸下的显示效果
5. 测试禁用状态下的组件行为

## 扩展功能

可以考虑添加以下扩展功能：

- 图片压缩功能
- 图片裁剪功能
- 批量删除功能
- 图片排序功能
- 图片标注功能
