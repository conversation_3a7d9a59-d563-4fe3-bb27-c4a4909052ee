<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="navbar-title">服务详情</view>
      <view class="navbar-right">
        <text class="more-icon">⋯</text>
        <text class="help-icon">○</text>
      </view>
    </view>
  </view>

  <!-- 服务地址 -->
  <view class="section">
    <view class="section-header">
      <view class="section-icon address-icon">📍</view>
      <text class="section-title">服务地址</text>
      <text class="edit-link" bindtap="editAddress">更换地址</text>
    </view>
    <view class="address-content">
      <text class="address-text">{{orderInfo.serviceAddress}}</text>
      <text class="phone-text">车主亲  {{orderInfo.customerPhone}}</text>
    </view>
  </view>

  <!-- 期望上门时间 -->
  <view class="section">
    <view class="section-header">
      <view class="section-icon time-icon">🕐</view>
      <text class="section-title">期望上门时间</text>
    </view>
    <view class="time-content">
      <view class="time-selector" bindtap="selectTime">
        <text class="{{orderInfo.expectTime ? 'time-selected' : 'time-placeholder'}}">
          {{orderInfo.expectTime || '+ 选择时间'}}
        </text>
      </view>
    </view>
  </view>

  <!-- 订金金额 -->
  <view class="section">
    <view class="section-header">
      <view class="section-icon price-icon">💰</view>
      <text class="section-title">订金金额</text>
    </view>
    <view class="price-content">
      <view class="price-item">
        <text class="price-label">服务费用</text>
        <text class="price-value">¥{{orderInfo.serviceFee}}</text>
      </view>
      <view class="price-item discount">
        <text class="price-label">折扣卡优惠</text>
        <text class="price-value discount-value">-¥{{orderInfo.discountAmount}}</text>
      </view>
      
      <!-- 优惠选择 -->
      <view class="coupon-section">
        <view class="coupon-item" bindtap="selectCoupon">
          <view class="coupon-icon">券</view>
          <view class="coupon-content">
            <text class="coupon-title">优惠选择</text>
            <text class="coupon-status">未使用 ></text>
          </view>
        </view>
        <view class="coupon-tag">
          <text class="coupon-text">1张5元用</text>
        </view>
      </view>

      <view class="total-price">
        <text class="total-label">合计</text>
        <text class="total-value">¥{{orderInfo.totalFee}}</text>
      </view>
    </view>
  </view>

  <!-- 备注说明 -->
  <view class="section">
    <view class="section-header">
      <view class="section-icon remark-icon">💬</view>
      <text class="section-title">备注说明</text>
    </view>
    <view class="remark-content">
      <textarea
        class="remark-textarea"
        placeholder="请输入备注信息..."
        value="{{orderInfo.remark}}"
        bindinput="onRemarkInput"
        maxlength="255"
      ></textarea>
      <view class="char-count">{{orderInfo.remark.length}}/255</view>
      
      <!-- 备注图片上传组件 -->
      <view class="remark-photos">
        <view class="photos-title">说明图片</view>
        <remark-photo-upload
          photoList="{{orderInfo.remarkPhotos}}"
          maxCount="9"
          keyPrefix="order-remark-photos/"
          placeholder="添加说明图片"
          bind:change="onRemarkPhotosChange"
        />
      </view>
    </view>
  </view>

  <!-- 底部提交按钮 -->
  <view class="bottom-actions">
    <button class="submit-btn" bindtap="submitOrder">
      去结算
    </button>
  </view>

  <!-- 时间选择器 -->
  <custom-picker
    wx:if="{{showTimePicker}}"
    bind:confirm="onTimeSelected"
    bind:cancel="onTimeCancel"
    selectedTime="{{selectedTime}}"
    hasBottomNavigation="{{false}}"
  />
</view>
