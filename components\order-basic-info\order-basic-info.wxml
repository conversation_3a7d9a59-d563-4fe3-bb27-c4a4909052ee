<view class="order-info">
  <view class="order-content" data-orderId="{{orderDetail.id}}">
    <image class="product-image" src="{{orderDetail.productImage}}"></image>
    <view class="product-info">
      <view class="flex align-center justify-between">
        <text class="product-name">{{orderDetail.productName}}</text>
      </view>
      <view class="flex align-center justify-between">
        <view class="product-service" wx:if="{{orderDetail.additionalServices && orderDetail.additionalServices.length > 0}}">
          增项服务：<text wx:for="{{orderDetail.additionalServices}}" wx:for-item="val" wx:key="val">{{val}}<text wx:if="{{index < orderDetail.additionalServices.length-1}}">、</text></text>
        </view>
        <view class="product-service" wx:else>增项服务：无</view>
      </view>
    </view>
  </view>

  <view class="order-details">
    <view class="detail-item">
      <text class="label">订单编号</text>
      <text class="content">{{orderDetail.sn}}</text>
    </view>
    <view class="detail-item">
      <text class="label">服务宠物</text>
      <text class="content">{{orderDetail.petName}}</text>
    </view>
    <view class="detail-item">
      <text class="label">宠物主人</text>
      <text class="content">{{orderDetail.customer.nickname || orderDetail.customer.name}}</text>
    </view>
    <view class="detail-item">
      <text class="label">联系电话</text>
      <text class="content">{{orderDetail.customer.phone || orderDetail.customer.mobile}}</text>
    </view>
    <view class="detail-item">
      <view class="label-row">
        <text class="label">期望上门时间</text>
        <view wx:if="{{orderDetail.status === '待服务'}}" class="edit-btn" bindtap="onReschedule">
          <text class="edit-text">修改</text>
        </view>
      </view>
      <text class="content">{{orderDetail.serviceTime}}</text>
    </view>
    <view class="detail-item">
      <view class="label-row">
        <text class="label">服务地址</text>
        <view wx:if="{{orderDetail.status === '待服务'}}" class="edit-btn" bindtap="onEditAddress">
          <text class="edit-text">修改</text>
        </view>
      </view>
      <view class="content" bindtap="onOpenNavigation" data-address="{{orderDetail.addressDetail}}" data-remark="{{orderDetail.addressRemark}}" data-latitude="{{orderDetail.latitude}}" data-longitude="{{orderDetail.longitude}}">
        <text>{{orderDetail.addressDetail}}</text>
        <text>📍</text>
      </view>
    </view>
    <view class="detail-item">
      <text class="label">最近出入口</text>
      <text class="content">{{orderDetail.addressRemark}}</text>
    </view>
    <view class="detail-item">
      <text class="label">创建时间</text>
      <text class="content">{{orderDetail.createdAt}}</text>
    </view>
    <!-- 用户备注 -->
    <view class="detail-item" wx:if="{{orderDetail.userRemark}}">
      <text class="label">用户备注</text>
      <text class="content user-remark">{{orderDetail.userRemark}}</text>
    </view>
    <!-- 备注照片 -->
    <view class="detail-item" wx:if="{{orderDetail.remarkPhotos && orderDetail.remarkPhotos.length > 0}}">
      <text class="label">备注照片</text>
      <view class="content">
        <view class="remark-photos">
          <image wx:for="{{orderDetail.remarkPhotos}}" wx:key="index"
                 src="{{item}}" class="remark-photo" mode="aspectFill"
                 bindtap="previewPhoto" data-url="{{item}}" data-urls="{{orderDetail.remarkPhotos}}">
          </image>
        </view>
      </view>
    </view>
    <!-- 金额信息 -->
    <view class="detail-item" wx:if="{{orderDetail.originalPrice}}">
      <text class="label">原价</text>
      <text class="content original-price">¥{{orderDetail.originalPrice}}</text>
    </view>
    <view class="detail-item">
      <text class="label">实付金额</text>
      <text class="content paid-money">{{orderDetail.totalFee}}</text>
    </view>
  </view>
</view>
