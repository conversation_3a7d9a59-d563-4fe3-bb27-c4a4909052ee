// src/utils/utils.js
function formatDate(timeStamp) {
  const date = safeParseDateForIOS(timeStamp);
  if (!date) return '';

  return `${date.getMonth() + 1}月${date.getDate()}日 ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
}
/**
 * 数据库返回时间的日常格式化，默认返回时分秒
 * @param {*} date
 * @param {*} showTime
 */
function formatNormalDate(date, showTime = true) {
  if (!date) return '';
  if (typeof date === 'string') {
    console.log('---------------------------:', date);
    date = safeParseDateForIOS(date);
    if (!date) {
      console.error('Invalid date format:', date);
      return '';
    }
  } else if (typeof date === 'number') {
    date = new Date(date * 1000); // 如果是时间戳，转换为毫秒
  } else if (!(date instanceof Date)) {
    console.error('Invalid date format:', date);
    return '';
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${showTime ? `${hours}:${minutes}:${seconds}` : ''}`;
}

function removeValue(array, valueToRemove) {
  let index = array.indexOf(valueToRemove);
  while (index !== -1) {
    array.splice(index, 1); // 删除指定索引的元素
    index = array.indexOf(valueToRemove); // 查找下一个指定值的索引
  }
  return array; // 返回修改后的原数组
}

function padZero(num) {
  return num < 10 ? '0' + num : num;
}

function generateId() {
  const timestamp = Date.now().toString(36); // 时间戳转36进制
  const random = Math.random().toString(36).substr(2, 5); // 随机数
  return `${timestamp}-${random}`;
}

function formatDateTime(date, format = 'YYYY-MM-DD') {
  if (!date) return '';

  const d = safeParseDateForIOS(date);
  if (!d || isNaN(d.getTime())) return '';

  const pad = n => (n < 10 ? `0${n}` : n);

  const replacements = {
    YYYY: d.getFullYear(),
    MM: pad(d.getMonth() + 1),
    DD: pad(d.getDate()),
    HH: pad(d.getHours()),
    mm: pad(d.getMinutes()),
    ss: pad(d.getSeconds()),
  };

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => replacements[match]);
}

/**
 * 安全的日期解析函数，兼容 iOS
 * @param {string|Date|number} dateInput - 日期输入
 * @returns {Date|null} - 解析后的日期对象，失败返回 null
 */
function safeParseDateForIOS(dateInput) {
  if (!dateInput) return null;

  // 如果已经是 Date 对象，直接返回
  if (dateInput instanceof Date) {
    return isNaN(dateInput.getTime()) ? null : dateInput;
  }

  // 如果是数字（时间戳），直接创建
  if (typeof dateInput === 'number') {
    const date = new Date(dateInput);
    return isNaN(date.getTime()) ? null : date;
  }

  // 如果是字符串，需要处理格式兼容性
  if (typeof dateInput === 'string') {
    // 将 "YYYY-MM-DD HH:mm:ss" 格式转换为 "YYYY/MM/DD HH:mm:ss" 格式（iOS 兼容）
    let dateStr = dateInput.trim();

    // 处理常见的不兼容格式
    if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateStr)) {
      // "2025-07-07 21:18:28" -> "2025/07/07 21:18:28"
      dateStr = dateStr.replace(/-/g, '/');
    } else if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
      // "2025-07-07" -> "2025/07/07"
      dateStr = dateStr.replace(/-/g, '/');
    }

    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }

  return null;
}

export { formatDate, generateId, padZero, formatNormalDate, removeValue, formatDateTime, safeParseDateForIOS };

export default {
  formatDate,
  generateId,
  padZero,
  formatNormalDate,
  removeValue,
  formatDateTime,
  safeParseDateForIOS,
};
