.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: #fff;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.more-icon,
.help-icon {
  font-size: 32rpx;
  color: #666;
}

/* 通用section样式 */
.section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  font-size: 24rpx;
}

.section-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.edit-link {
  font-size: 28rpx;
  color: #3b82f6;
}

/* 服务地址 */
.address-content {
  padding-left: 48rpx;
}

.address-text {
  display: block;
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.phone-text {
  font-size: 28rpx;
  color: #666;
}

/* 期望上门时间 */
.time-content {
  padding-left: 48rpx;
}

.time-selector {
  display: inline-block;
  padding: 16rpx 32rpx;
  border: 2rpx solid #ff69b4;
  border-radius: 50rpx;
  background-color: #fff;
}

.time-placeholder {
  color: #ff69b4;
  font-size: 28rpx;
}

.time-selected {
  color: #333;
  font-size: 28rpx;
}

/* 订金金额 */
.price-content {
  padding-left: 48rpx;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.price-label {
  font-size: 30rpx;
  color: #333;
}

.price-value {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

.discount-value {
  color: #10b981;
}

/* 优惠选择 */
.coupon-section {
  margin: 24rpx 0;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #ff69b4;
}

.coupon-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.coupon-icon {
  width: 48rpx;
  height: 32rpx;
  background-color: #ff69b4;
  color: #fff;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
  margin-right: 16rpx;
}

.coupon-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-title {
  font-size: 28rpx;
  color: #333;
}

.coupon-status {
  font-size: 26rpx;
  color: #666;
}

.coupon-tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  background-color: #ff69b4;
  color: #fff;
  font-size: 24rpx;
  border-radius: 20rpx;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #e5e5e5;
}

.total-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.total-value {
  font-size: 36rpx;
  color: #ff69b4;
  font-weight: 700;
}

/* 备注说明 */
.remark-content {
  padding-left: 48rpx;
}

.remark-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
  margin-bottom: 12rpx;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
}

.remark-photos {
  margin-top: 24rpx;
}

.photos-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #e5e5e5;
  z-index: 100;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #ff69b4;
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:active {
  background-color: #e91e63;
}
